<template>
  <div class="knowledge-graph-container">
    <!-- 加载遮罩 -->
    <LoadingOverlay v-if="uiState.loading" :message="uiState.loadingMessage" />

    <!-- 错误提示 -->
    <div v-if="uiState.error" class="error-overlay">
      <div class="error-content">
        <h3>❌ 加载失败</h3>
        <p>{{ uiState.error }}</p>
        <button class="btn btn-primary" @click="retryLoad">重试</button>
      </div>
    </div>

    <!-- 主界面 -->
    <div v-else class="main-layout">
      <!-- 侧边栏 -->
      <Sidebar
        :collapsed="uiState.sidebarCollapsed"
        :width="uiState.sidebarWidth"
        @toggle="uiState.toggleSidebar"
      />

      <!-- 图谱画布区域 -->
      <div class="canvas-area" :style="{ marginLeft: uiState.sidebarWidth + 'px' }">
        <GraphCanvas />

        <!-- 性能监控面板 -->
        <PerformancePanel v-if="uiState.showFPS" />

        <!-- 详情面板 -->
        <DetailPanel
          v-if="uiState.showDetailPanel"
          :data="uiState.detailPanelData"
          :type="uiState.detailPanelType"
          @close="uiState.hideDetailPanel"
        />
      </div>
    </div>

    <!-- 工具提示 -->
    <div
      v-if="uiState.tooltip.visible"
      class="tooltip"
      :style="{
        left: uiState.tooltip.x + 'px',
        top: uiState.tooltip.y + 'px',
      }"
    >
      {{ uiState.tooltip.content }}
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { useGraphDataStore } from '../stores/graphData.js'
import { useUIStateStore } from '../stores/uiState.js'
import Sidebar from './Sidebar.vue'
import GraphCanvas from './GraphCanvas.vue'
import DetailPanel from './DetailPanel.vue'
import LoadingOverlay from './LoadingOverlay.vue'
import PerformancePanel from './PerformancePanel.vue'

// 使用 stores
const graphData = useGraphDataStore()
const uiState = useUIStateStore()

/**
 * 初始化应用
 */
async function initializeApp() {
  try {
    uiState.setLoading(true, '正在加载图数据...')

    // 恢复UI状态
    uiState.restoreState()

    // 加载图数据
    const success = await graphData.loadGraphData()

    // 恢复筛选状态（在数据加载后）
    if (success) {
      graphData.restoreFilterState()
    }

    if (!success) {
      throw new Error('无法加载图数据文件')
    }

    uiState.setLoading(false)

    console.log('知识图谱应用初始化完成')
  } catch (error) {
    console.error('初始化失败:', error)
    uiState.setLoading(false)
    uiState.setError(error.message || '初始化失败')
  }
}

/**
 * 重试加载
 */
async function retryLoad() {
  uiState.clearError()
  await initializeApp()
}

/**
 * 处理窗口大小变化
 */
function handleResize() {
  const width = window.innerWidth - uiState.sidebarWidth
  const height = window.innerHeight
  uiState.updateCanvasSize(width, height)
}

/**
 * 处理键盘快捷键
 */
function handleKeydown(event) {
  // ESC 键清除选择
  if (event.key === 'Escape') {
    uiState.clearSelection()
  }

  // 空格键切换物理引擎
  if (event.key === ' ' && !event.target.matches('input, textarea')) {
    event.preventDefault()
    uiState.togglePhysics()
  }

  // R 键重置视图
  if (event.key === 'r' || event.key === 'R') {
    if (!event.target.matches('input, textarea')) {
      event.preventDefault()
      uiState.resetViewTransform()
    }
  }

  // F 键切换全屏
  if (event.key === 'f' || event.key === 'F') {
    if (!event.target.matches('input, textarea')) {
      event.preventDefault()
      toggleFullscreen()
    }
  }
}

/**
 * 切换全屏模式
 */
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

/**
 * 组件挂载时的初始化
 */
onMounted(async () => {
  // 初始化应用
  await initializeApp()

  // 设置初始画布大小
  handleResize()

  // 添加事件监听器
  window.addEventListener('resize', handleResize)
  window.addEventListener('keydown', handleKeydown)

  // 定期保存状态
  const saveInterval = setInterval(() => {
    uiState.saveState()
  }, 30000) // 每30秒保存一次

  // 清理函数
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('keydown', handleKeydown)
    clearInterval(saveInterval)
    uiState.saveState() // 组件卸载时保存状态
  })
})
</script>

<style scoped>
.knowledge-graph-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
}

.main-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

.canvas-area {
  flex: 1;
  position: relative;
  transition: margin-left 0.3s ease;
  overflow: hidden;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid #ff6b6b;
  border-radius: 12px;
  padding: 32px;
  text-align: center;
  max-width: 400px;
}

.error-content h3 {
  margin-bottom: 16px;
  color: #ff6b6b;
  font-size: 18px;
}

.error-content p {
  margin-bottom: 24px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .knowledge-graph-container {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .knowledge-graph-container {
    font-size: 12px;
  }

  .canvas-area {
    margin-left: 0 !important;
  }

  .error-content {
    margin: 20px;
    padding: 24px;
    max-width: calc(100vw - 40px);
  }

  .error-content h3 {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .knowledge-graph-container {
    font-size: 11px;
  }

  .error-content {
    margin: 10px;
    padding: 16px;
    max-width: calc(100vw - 20px);
  }

  .error-content h3 {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .error-content p {
    margin-bottom: 16px;
    font-size: 12px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .knowledge-graph-container {
    /* 禁用悬停效果，优化触摸体验 */
    --hover-disabled: true;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .knowledge-graph-container {
    /* 高DPI屏幕的优化 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
  .error-content {
    max-height: 80vh;
    overflow-y: auto;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .knowledge-graph-container {
    /* 已经是深色主题，保持不变 */
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .canvas-area {
    transition: none;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
