import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 图数据管理 Store
 * 负责管理原始数据、筛选后的数据和相关的计算属性
 */
export const useGraphDataStore = defineStore('graphData', () => {
  // 原始数据
  const rawNodes = ref([])
  const rawEdges = ref([])

  // 筛选条件
  const minDegree = ref(0)
  const maxDegree = ref(500)
  const selectedTypes = ref(new Set())
  const searchQuery = ref('')
  const maxNodes = ref(1000)

  // 所有可用的节点类型
  const availableTypes = computed(() => {
    const types = new Set()
    rawNodes.value.forEach((node) => {
      if (node.type && node.type.trim()) {
        types.add(node.type)
      }
    })
    return Array.from(types).sort()
  })

  // 筛选后的节点
  const filteredNodes = computed(() => {
    let nodes = rawNodes.value.filter((node) => {
      // 度数范围筛选
      if (node.degree < minDegree.value || node.degree > maxDegree.value) {
        return false
      }

      // 类型筛选
      if (selectedTypes.value.size > 0 && !selectedTypes.value.has(node.type)) {
        return false
      }

      // 搜索筛选
      if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase()
        const title = (node.title || '').toLowerCase()
        const description = (node.description || '').toLowerCase()
        if (!title.includes(query) && !description.includes(query)) {
          return false
        }
      }

      return true
    })

    // 限制最大节点数
    if (nodes.length > maxNodes.value) {
      // 按度数排序，保留重要节点
      nodes = nodes.sort((a, b) => b.degree - a.degree).slice(0, maxNodes.value)
    }

    return nodes
  })

  // 筛选后的边
  const filteredEdges = computed(() => {
    const nodeIds = new Set(filteredNodes.value.map((node) => node.id))
    return rawEdges.value.filter((edge) => nodeIds.has(edge.source) && nodeIds.has(edge.target))
  })

  // 统计信息
  const stats = computed(() => ({
    totalNodes: rawNodes.value.length,
    totalEdges: rawEdges.value.length,
    visibleNodes: filteredNodes.value.length,
    visibleEdges: filteredEdges.value.length,
  }))

  // 度数级别统计
  const degreeStats = computed(() => {
    const levels = {
      superNodes: { min: 21, max: Infinity, count: 0, color: '#FF6B6B' },
      importantNodes: { min: 11, max: 20, count: 0, color: '#4ECDC4' },
      activeNodes: { min: 6, max: 10, count: 0, color: '#45B7D1' },
      normalNodes: { min: 3, max: 5, count: 0, color: '#96CEB4' },
      edgeNodes: { min: 1, max: 2, count: 0, color: '#FFEAA7' },
      isolatedNodes: { min: 0, max: 0, count: 0, color: '#DDA0DD' },
    }

    filteredNodes.value.forEach((node) => {
      const degree = node.degree
      if (degree > 20) levels.superNodes.count++
      else if (degree >= 11) levels.importantNodes.count++
      else if (degree >= 6) levels.activeNodes.count++
      else if (degree >= 3) levels.normalNodes.count++
      else if (degree >= 1) levels.edgeNodes.count++
      else levels.isolatedNodes.count++
    })

    return levels
  })

  /**
   * 加载图数据
   * @param {string} url - 数据文件URL
   */
  async function loadGraphData(url = '/graph_data.json') {
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()

      rawNodes.value = data.nodes || []
      rawEdges.value = data.edges || []

      // 初始化所有类型为选中状态
      selectedTypes.value = new Set(availableTypes.value)

      // 设置度数范围的初始值
      if (rawNodes.value.length > 0) {
        const degrees = rawNodes.value.map((node) => node.degree)
        minDegree.value = Math.min(...degrees)
        maxDegree.value = Math.max(...degrees)
      }

      console.log('图数据加载成功:', {
        nodes: rawNodes.value.length,
        edges: rawEdges.value.length,
        types: availableTypes.value.length,
      })

      return true
    } catch (error) {
      console.error('加载图数据失败:', error)
      return false
    }
  }

  /**
   * 更新筛选条件
   */
  function updateDegreeRange(min, max) {
    minDegree.value = min
    maxDegree.value = max
    saveFilterState()
  }

  function updateSelectedTypes(types) {
    selectedTypes.value = new Set(types)
    saveFilterState()
  }

  function updateSearchQuery(query) {
    searchQuery.value = query
    saveFilterState()
  }

  function updateMaxNodes(max) {
    maxNodes.value = max
    saveFilterState()
  }

  /**
   * 重置筛选条件
   */
  function resetFilters() {
    if (rawNodes.value.length > 0) {
      const degrees = rawNodes.value.map((node) => node.degree)
      minDegree.value = Math.min(...degrees)
      maxDegree.value = Math.max(...degrees)
    }
    selectedTypes.value = new Set(availableTypes.value)
    searchQuery.value = ''
    maxNodes.value = 1000
    saveFilterState()
  }

  /**
   * 根据ID获取节点
   */
  function getNodeById(id) {
    return filteredNodes.value.find((node) => node.id === id)
  }

  /**
   * 根据ID获取边
   */
  function getEdgeById(id) {
    return filteredEdges.value.find((edge) => edge.id === id)
  }

  /**
   * 保存筛选状态到 localStorage
   */
  function saveFilterState() {
    try {
      const state = {
        minDegree: minDegree.value,
        maxDegree: maxDegree.value,
        selectedTypes: Array.from(selectedTypes.value),
        searchQuery: searchQuery.value,
        maxNodes: maxNodes.value,
      }
      localStorage.setItem('knowledge-graph-filter-state', JSON.stringify(state))
    } catch (error) {
      console.warn('保存筛选状态失败:', error)
    }
  }

  /**
   * 从 localStorage 恢复筛选状态
   */
  function restoreFilterState() {
    try {
      const saved = localStorage.getItem('knowledge-graph-filter-state')
      if (saved) {
        const state = JSON.parse(saved)
        minDegree.value = state.minDegree ?? 0
        maxDegree.value = state.maxDegree ?? 500
        selectedTypes.value = new Set(state.selectedTypes ?? [])
        searchQuery.value = state.searchQuery ?? ''
        maxNodes.value = state.maxNodes ?? 1000
      }
    } catch (error) {
      console.warn('恢复筛选状态失败:', error)
    }
  }

  /**
   * 清除保存的状态
   */
  function clearSavedState() {
    try {
      localStorage.removeItem('knowledge-graph-filter-state')
    } catch (error) {
      console.warn('清除保存状态失败:', error)
    }
  }

  return {
    // 状态
    rawNodes,
    rawEdges,
    minDegree,
    maxDegree,
    selectedTypes,
    searchQuery,
    maxNodes,

    // 计算属性
    availableTypes,
    filteredNodes,
    filteredEdges,
    stats,
    degreeStats,

    // 方法
    loadGraphData,
    updateDegreeRange,
    updateSelectedTypes,
    updateSearchQuery,
    updateMaxNodes,
    resetFilters,
    getNodeById,
    getEdgeById,
    saveFilterState,
    restoreFilterState,
    clearSavedState,
  }
})
